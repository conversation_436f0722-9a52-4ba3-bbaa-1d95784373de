<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Models\Project;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

final class CompanyVerificationMiddleware
{
    /**
     * Handle an incoming request.
     *
     * Check if company verification_at is null and redirect SMS to company phone if so.
     *
     * @param  Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get the project from the bearer token
        $project = Project::where('token', $request->bearerToken())->first();

        if (! $project) {
            return response()->json(
                ['message' => 'Invalid or missing token.'],
                401,
            );
        }

        $company = $project->company;

        if (! $company) {
            return response()->json(
                ['message' => 'Company not found.'],
                404,
            );
        }

        // Check if company verification_at is null
        if (is_null($company->verification_at)) {
            // Modify the request to set receiver to company phone
            $this->redirectToCompanyPhone($request, $company->phone);
        }

        return $next($request);
    }

    /**
     * Redirect SMS receiver to company phone number
     */
    private function redirectToCompanyPhone(Request $request, string $companyPhone): void
    {
        // Handle single receiver (send, send_template endpoints)
        if ($request->has('receiver')) {
            $request->merge(['receiver' => $companyPhone]);
        }

        // Handle bulk receivers (bulk_send endpoint)
        if ($request->has('receivers')) {
            $request->merge(['receivers' => [$companyPhone]]);
        }

        // Note: contacts_send uses contact_group_id, so we don't modify it here
        // as it would require more complex logic to create/modify contact groups
    }
}
